import { useUserStore } from "@/stores/modules/user";
import { pushDeviceConfigJwe, renameDevice } from "@/api/modules/project";
import { ElMessage } from "element-plus";
import i18n from "@/languages";
import { drawerProps, deviceNameChanged, editName, progressShow, downloadingPer } from "./state";

const t = i18n.global.t;

/**
 * 重启设备
 */
export const rebootDevice = async (): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          sysReboot: 1
        }
      }
    };

    console.log("重启设备参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.rebootSuccess") });
  } catch (error) {
    console.error("重启设备失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 保存设备配置
 */
export const saveDeviceConfig = async (): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          sysSave: 1
        }
      }
    };

    console.log("保存配置参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.saveSuccess") });
  } catch (error) {
    console.error("保存配置失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 编辑设备名称
 */
export const editDeviceName = (row: any): void => {
  console.log("editDeviceName called. row:", row);
  editName.value = !editName.value;
};

/**
 * 保存设备名称
 */
export const saveDeviceName = async (row: any): Promise<void> => {
  console.log("saveDeviceName called. row:", JSON.stringify(row));

  try {
    const response = await renameDevice({
      deviceId: row.deviceId,
      deviceName: row.deviceName
    });

    console.log("API 响应数据:", response);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.operationSuccess") });
    deviceNameChanged.value = true;
    editName.value = false;
  } catch (error) {
    console.error("保存设备名称失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 升级设备固件
 */
export const upgradeDevice = async (firmwareUrl: string): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  if (!firmwareUrl) {
    ElMessage.error({ message: t("common.firmwareUrlRequired") });
    return;
  }

  try {
    progressShow.value = true;
    downloadingPer.value = 0;

    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          sysUpgrade: {
            url: firmwareUrl
          }
        }
      }
    };

    console.log("升级设备参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      progressShow.value = false;
      return;
    }

    // 模拟升级进度
    const progressInterval = setInterval(() => {
      downloadingPer.value += 10;
      if (downloadingPer.value >= 100) {
        clearInterval(progressInterval);
        progressShow.value = false;
        downloadingPer.value = 0;
        ElMessage.success({ message: t("common.upgradeSuccess") });
      }
    }, 1000);
  } catch (error) {
    console.error("升级设备失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
    progressShow.value = false;
    downloadingPer.value = 0;
  }
};

/**
 * 恢复出厂设置
 */
export const resetToFactory = async (): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          sysReset: 1
        }
      }
    };

    console.log("恢复出厂设置参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.resetSuccess") });
  } catch (error) {
    console.error("恢复出厂设置失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};
