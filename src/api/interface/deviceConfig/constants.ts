import { PoePower, VlanMode, TrafficType } from "./types";
import { getDescription } from "@/api/interface/device/formatter";

/**
 * 默认需要分段请求的字段列表（可扩展）
 */
export const DEFAULT_SEGMENTED_FIELDS = ["swPoe", "swStorm", "swPort", "swVlan"] as const;

/**
 * 字段分类映射（可扩展）
 */
export const FIELD_CATEGORY_MAP: Record<string, "system" | "network" | "wireless"> = {
  // 系统相关字段
  swPoe: "system",
  swStorm: "system",
  swPort: "system",
  topology: "system",
  sysReboot: "system",
  sysPassword: "system",
  sysSave: "system",
  led: "system",
  userList: "system",
  apList: "system",
  apGroup: "system",

  // 网络相关字段
  swVlan: "network",
  swLldp: "network",
  swRstp: "network",
  swIsolate: "network",
  wan: "network",
  lan: "network",
  dhcp: "network",
  brAp: "network",
  join: "network",
  brSafe: "network",

  // 无线相关字段
  wifiTime: "wireless",
  radio0: "wireless",
  radio1: "wireless",
  guest: "wireless"
};

/**
 * PoE功率选项
 */
export const poePowerOptions = {
  [PoePower.AF]: "af (15.4w)",
  [PoePower.AT]: "at (30w)"
};

/**
 * VLAN模式选项
 */
export const vlanModeOptions = {
  [VlanMode.ACCESS]: "access",
  [VlanMode.TRUNK]: "trunk",
  [VlanMode.HYBRID]: "hybrid"
};

/**
 * 流量类型选项数组
 */
export const trafficTypeOptionsArray = Object.keys(TrafficType)
  .filter(key => isNaN(Number(key))) // 过滤掉数字的部分
  .map(key => ({
    label: key, // 选项的显示文本
    value: TrafficType[key as keyof typeof TrafficType], // 对应的值
    description: getDescription(key) // 添加中文描述
  }));

/**
 * 默认时间值
 */
export const defaultBeginTime = new Date("1970-01-01T00:00:00");
export const defaultEndTime = new Date("1970-01-01T23:59:59");
