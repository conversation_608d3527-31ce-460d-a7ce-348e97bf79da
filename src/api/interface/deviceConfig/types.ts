import type { DeviceConfigData, DeviceWeekStatistics } from "@/api/interface/device/model";

/**
 * 设备数据加载命令类型
 */
export enum DeviceDataCmd {
  CONFIG = 10, // 设备配置
  STATUS = 4 // 设备状态
}

/**
 * 设备数据类型
 */
export enum DeviceDataType {
  CONFIG = "设备配置",
  STATUS = "设备状态"
}

/**
 * 加载设备数据的参数接口
 */
export interface LoadDeviceDataParams {
  cmd: DeviceDataCmd;
  deviceId: string;
  userId: string;
  data: DeviceConfigData;
  sequenceNo?: number; // 分段数据序号
}

/**
 * 字段请求结果接口
 */
export interface FieldRequestResult {
  field: string;
  category: "system" | "network" | "wireless";
  data: any;
  success: boolean;
  error?: Error;
}

/**
 * PoE功率枚举
 */
export enum PoePower {
  AF = 0, // af (15.4w)
  AT = 1 // at (30w)
}

/**
 * VLAN模式枚举
 */
export enum VlanMode {
  ACCESS = 0, // access
  TRUNK = 1, // trunk
  HYBRID = 2 // hybrid
}

/**
 * 流量类型枚举
 */
export enum TrafficType {
  UNICAST = 4, // unicast
  MULTICAST = 1, // multicast
  BROADCAST = 2 // broadcast
}

/**
 * 设备统计数据类型
 */
export type DeviceStatistics =
  | DeviceWeekStatistics
  | {
      deviceId: string;
      deviceName: string;
      beginDate: string;
      beginDateTimeZone?: unknown;
      endDate: string;
      endDateTimeZone?: unknown;
      bootTime: number;
      deviceType: string;
      deviceModel: string;
      rxByte: number;
      txByte: number;
      statistics: { date: string; rxByte: number; txByte: number; hour?: null }[];
    }
  | null;
