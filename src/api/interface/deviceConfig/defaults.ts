import { PoePower, VlanMode, TrafficType } from "./types";

/**
 * 获取默认设备配置
 */
export const getDefaultDeviceConfig = (): any => {
  return {
    system: {
      swPort: [],
      swPoe: [],
      swStorm: []
    },
    network: {
      swVlan: [],
      swIsolate: [],
      wan: [],
      lan: {
        ipaddr: "***********",
        netmask: "*************"
      },
      dhcp: {
        enable: 1,
        start: 100,
        end: 200,
        lease: 86400
      }
    },
    wireless: {
      radio0: {
        ssid: "",
        key: "",
        channel: 0,
        txpower: 20
      },
      radio1: {
        ssid: "",
        key: "",
        channel: 0,
        txpower: 20
      },
      guest: {
        ssid: "",
        key: ""
      },
      wifiTime: {
        enabled: 0,
        beginTime: "00:00",
        endTime: "23:59"
      }
    }
  };
};

/**
 * 获取默认设备状态
 */
export const getDefaultDeviceStatus = (): any => {
  return {
    system: {
      deviceInfo: {
        deviceId: "",
        deviceName: "",
        deviceType: "",
        deviceModel: "",
        firmwareVersion: "",
        hardwareVersion: "",
        macAddress: "",
        serialNumber: "",
        uptime: 0,
        temperature: 0
      }
    },
    network: {},
    wireless: {}
  };
};

/**
 * 获取默认端口配置
 */
export const getDefaultPortConfig = () => {
  return {
    port: 1,
    enable: 1,
    speed: 0, // AUTO
    duplex: 1,
    flowControl: 0,
    description: ""
  };
};

/**
 * 获取默认PoE配置
 */
export const getDefaultPoEConfig = () => {
  return {
    port: 1,
    enable: 1,
    power: PoePower.AF,
    priority: 0
  };
};

/**
 * 获取默认VLAN配置
 */
export const getDefaultVlanConfig = () => {
  return {
    port: 1,
    mode: VlanMode.ACCESS,
    pvid: 1,
    tagged: [],
    untagged: [1]
  };
};

/**
 * 获取默认端口隔离配置
 */
export const getDefaultIsolationConfig = () => {
  return {
    port: 1,
    isolate: []
  };
};

/**
 * 获取默认风暴控制配置
 */
export const getDefaultStormConfig = () => {
  return {
    port: 1,
    enable: 0,
    type: TrafficType.BROADCAST,
    rate: 1000
  };
};

/**
 * 获取默认QoS配置
 */
export const getDefaultQosConfig = () => {
  return {
    port: 1,
    enable: 0,
    priority: 0,
    ingressRate: 0,
    egressRate: 0
  };
};

/**
 * 获取默认无线配置
 */
export const getDefaultWirelessConfig = () => {
  return {
    enable: 1,
    ssid: "",
    key: "",
    channel: 0,
    txpower: 20,
    encrypt: 0,
    hidden: 0,
    maxClients: 32
  };
};

/**
 * 获取默认WAN配置
 */
export const getDefaultWanConfig = () => {
  return {
    type: 0, // DHCP
    ipaddr: "",
    netmask: "",
    gateway: "",
    dns1: "*******",
    dns2: "*******",
    mtu: 1500
  };
};

/**
 * 获取默认LAN配置
 */
export const getDefaultLanConfig = () => {
  return {
    ipaddr: "***********",
    netmask: "*************"
  };
};

/**
 * 获取默认DHCP配置
 */
export const getDefaultDhcpConfig = () => {
  return {
    enable: 1,
    start: "***********00",
    end: "*************",
    lease: 86400,
    gateway: "***********",
    dns1: "***********",
    dns2: "*******"
  };
};

/**
 * 获取默认LLDP配置
 */
export const getDefaultLldpConfig = () => {
  return {
    enable: 0,
    interval: 30,
    holdTime: 120
  };
};

/**
 * 获取默认RSTP配置
 */
export const getDefaultRstpConfig = () => {
  return {
    enable: 0,
    priority: 32768,
    maxAge: 20,
    helloTime: 2,
    forwardDelay: 15
  };
};

/**
 * 获取默认系统配置
 */
export const getDefaultSystemConfig = () => {
  return {
    deviceName: "",
    location: "",
    contact: "",
    description: "",
    timezone: "UTC+8",
    ntpServer: "pool.ntp.org",
    syslogServer: "",
    snmpCommunity: "public"
  };
};
