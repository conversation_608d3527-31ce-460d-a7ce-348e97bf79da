import { getDeviceConfigJwe } from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { removeEmptyValues } from "@/utils";
import { merge } from "lodash";
import { drawerProps, deviceConfig, deviceStatus } from "./state";
import { shouldFieldBeSegmented, inferFieldCategory } from "./segmentation";
import { DeviceDataCmd, DeviceDataType } from "./types";
import type { LoadDeviceDataParams } from "./types";

/**
 * 生成两位数的随机数（10-99）
 */
const generateRandomTwoDigits = (): number => {
  return Math.floor(Math.random() * 90) + 10;
};

/**
 * 格式化数字为两位数字符串
 */
const formatTwoDigits = (num: number): string => {
  return num < 10 ? `0${num}` : `${num}`;
};

/**
 * 过滤空数据
 */
function filterEmptyData(data: any) {
  return removeEmptyValues(data);
}

/**
 * 发送单个分段请求
 */
const makeSegmentRequest = async (
  field: string,
  category: "system" | "network" | "wireless",
  baseParams: Omit<LoadDeviceDataParams, "data" | "sequenceNo">,
  randomPrefix: number,
  currentSequence: number
): Promise<any> => {
  const formattedPrefix = formatTwoDigits(randomPrefix);
  const formattedSequence = formatTwoDigits(currentSequence);
  const sequenceNoStr = `${formattedPrefix}${formattedSequence}`;
  const sequenceNo = parseInt(sequenceNoStr, 10);

  const requestParams: LoadDeviceDataParams = {
    ...baseParams,
    data: { [category]: [field] },
    sequenceNo
  };

  console.log(
    `🚀 正在加载字段 ${field} 第${currentSequence}段，sequenceNo: ${sequenceNo} (前缀:${formattedPrefix}, 段号:${formattedSequence})`
  );

  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount <= maxRetries) {
    try {
      const response = await getDeviceConfigJwe(requestParams);
      if (!response?.data) {
        throw new Error(`获取字段 ${field} 第${currentSequence}段失败: 响应数据为空`);
      }
      return response;
    } catch (error) {
      console.error(`获取字段 ${field} 第${currentSequence}段失败:`, error);
      retryCount++;
      if (retryCount <= maxRetries) {
        console.log(`第${retryCount}次重试获取字段 ${field} 第${currentSequence}段...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      } else {
        console.error(`获取字段 ${field} 第${currentSequence}段失败，已重试${maxRetries}次`);
        throw error;
      }
    }
  }
  throw new Error(`获取字段 ${field} 第${currentSequence}段失败，超出最大重试次数`);
};

/**
 * 处理单个字段的分段请求
 */
const handleSingleFieldSegmentedRequest = async (
  field: string,
  category: "system" | "network" | "wireless",
  baseParams: Omit<LoadDeviceDataParams, "data" | "sequenceNo">,
  randomPrefix: number
): Promise<any> => {
  let allData = {};
  let currentSequence = 1;
  let totalSegments = 1;
  let isComplete = false;

  try {
    console.log(`🚀 开始处理字段 ${field} 的分段请求，随机前缀: ${formatTwoDigits(randomPrefix)} (${randomPrefix})`);

    while (!isComplete) {
      console.log(`🔄 字段 ${field} 请求第${currentSequence}段，使用前缀: ${formatTwoDigits(randomPrefix)}`);
      const response = await makeSegmentRequest(field, category, baseParams, randomPrefix, currentSequence);
      const filteredData = filterEmptyData(response.data);

      // 合并当前分段的数据
      if (filteredData[category] && filteredData[category][field]) {
        if (!allData[category]) {
          allData[category] = {};
        }
        const currentSegmentData = filteredData[category][field];

        if (Array.isArray(currentSegmentData)) {
          if (!allData[category][field]) {
            allData[category][field] = [];
          }
          allData[category][field] = [...allData[category][field], ...currentSegmentData];
        } else if (typeof currentSegmentData === "object" && currentSegmentData !== null) {
          if (!allData[category][field] || typeof allData[category][field] !== "object") {
            allData[category][field] = {};
          }
          merge(allData[category][field], currentSegmentData);
        } else {
          allData[category][field] = currentSegmentData;
        }
      }

      // 检查响应中的sequenceNo
      if (response && typeof response === "object" && "sequenceNo" in response) {
        const responseSequenceNo = response.sequenceNo;
        const responseSequenceNoStr = responseSequenceNo.toString().padStart(4, "0");
        const responsePrefix = parseInt(responseSequenceNoStr.substring(0, 2), 10);
        const responseSegment = parseInt(responseSequenceNoStr.substring(2, 4), 10);

        // 验证前缀是否匹配
        if (responsePrefix !== randomPrefix) {
          throw new Error(`字段 ${field} sequenceNo前缀不匹配，期望: ${randomPrefix}，实际: ${responsePrefix}`);
        }

        totalSegments = responseSegment;
        const currentRequestSequenceNo = parseInt(`${formatTwoDigits(randomPrefix)}${formatTwoDigits(currentSequence)}`, 10);

        if (currentRequestSequenceNo === responseSequenceNo) {
          isComplete = true;
          console.log(`字段 ${field} 分段请求完成！共${totalSegments}段数据`);
        } else {
          currentSequence++;
          if (currentSequence > totalSegments) {
            throw new Error(`字段 ${field} 分段请求超出总段数(${totalSegments})`);
          }
        }
      } else {
        isComplete = true;
        console.log(`字段 ${field} 响应中无sequenceNo，视为单段数据`);
      }
    }
  } catch (error) {
    console.error(`处理字段 ${field} 的分段请求失败:`, error);
    throw error;
  }

  console.log(`✅ 字段 ${field} 数据加载完成，共${totalSegments}段`);
  return allData;
};

/**
 * 处理普通字段请求（非分段）
 */
const handleNormalFieldsRequest = async (
  fields: string[],
  category: "system" | "network" | "wireless",
  baseParams: Omit<LoadDeviceDataParams, "data">
): Promise<any> => {
  const requestParams: LoadDeviceDataParams = {
    ...baseParams,
    data: { [category]: fields }
  };

  console.log(`正在加载普通字段 ${category}: [${fields.join(", ")}]`);

  try {
    const response = await getDeviceConfigJwe(requestParams);

    if (!response?.data) {
      console.warn(`获取普通字段 ${category} 的响应数据为空，返回空对象`);
      return {};
    }

    const filteredData = filterEmptyData(response.data);
    console.log(`普通字段 ${category} 加载完成:`, filteredData);
    return filteredData;
  } catch (error) {
    console.error(`获取普通字段 ${category} 失败:`, error);
    throw error;
  }
};

/**
 * 分析需要请求的字段并分组
 */
const analyzeFieldRequests = (supports: any, forceSegmentedFields?: string[]) => {
  const fieldGroups: {
    segmentedFields: Array<{ field: string; category: "system" | "network" | "wireless" }>;
    normalFields: Record<"system" | "network" | "wireless", string[]>;
  } = {
    segmentedFields: [],
    normalFields: { system: [], network: [], wireless: [] }
  };

  if (!supports) {
    console.warn(`⚠️ supports 为空，无法分析字段请求`);
    return fieldGroups;
  }

  console.log(`🔍 分析字段请求，supports:`, supports);

  const supportTypes = ["wireless", "network", "system"] as const;
  supportTypes.forEach(supportType => {
    if (supports[supportType] && supports[supportType].supports) {
      const supportedFields = supports[supportType].supports as string[];
      console.log(`📋 ${supportType} 支持的字段:`, supportedFields);

      supportedFields.forEach(field => {
        const category = inferFieldCategory(field, supportType);
        const shouldSegment = forceSegmentedFields ? forceSegmentedFields.includes(field) : shouldFieldBeSegmented(field);

        if (shouldSegment) {
          fieldGroups.segmentedFields.push({ field, category });
        } else {
          fieldGroups.normalFields[category].push(field);
        }
      });
    }
  });

  return fieldGroups;
};

/**
 * 加载设备配置数据（智能分段）
 */
export const loadDeviceConfig = async (): Promise<void> => {
  console.log("🚀 开始加载设备配置数据（智能分段）");
  await loadDeviceData(DeviceDataCmd.CONFIG, DeviceDataType.CONFIG);
};

/**
 * 加载设备状态数据（智能分段）
 */
export const loadDeviceStatus = async (): Promise<void> => {
  console.log("🚀 开始加载设备状态数据（智能分段）");
  await loadDeviceData(DeviceDataCmd.STATUS, DeviceDataType.STATUS);
};

/**
 * 加载设备数据的核心函数
 */
const loadDeviceData = async (cmd: DeviceDataCmd, dataType: DeviceDataType): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    console.error(`❌ 无法加载${dataType}：设备ID为空`);
    return;
  }

  console.log(`🚀 开始加载${dataType}，设备ID: ${deviceId}`);

  try {
    const baseParams = { cmd, deviceId, userId };
    const targetData = cmd === DeviceDataCmd.CONFIG ? deviceConfig : deviceStatus;

    // 获取设备支持的字段
    const supportsResponse = await getDeviceConfigJwe({
      ...baseParams,
      data: { supports: true }
    });

    if (!supportsResponse?.data) {
      console.error(`❌ 获取设备支持字段失败`);
      return;
    }

    const supports = supportsResponse.data;
    const { segmentedFields, normalFields } = analyzeFieldRequests(supports);

    console.log(`📊 字段分析结果:`, {
      分段字段: segmentedFields.map(f => f.field),
      普通字段: normalFields
    });

    // 并行处理分段字段
    const segmentedPromises = segmentedFields.map(async ({ field, category }) => {
      const randomPrefix = generateRandomTwoDigits();
      try {
        const result = await handleSingleFieldSegmentedRequest(field, category, baseParams, randomPrefix);
        return { field, category, data: result, success: true };
      } catch (error) {
        console.error(`字段 ${field} 分段请求失败:`, error);
        return { field, category, data: {}, success: false, error };
      }
    });

    // 并行处理普通字段
    const normalPromises = Object.entries(normalFields).map(async ([category, fields]) => {
      if (fields.length === 0) return { category, data: {}, success: true };

      try {
        const result = await handleNormalFieldsRequest(fields, category as any, baseParams);
        return { category, data: result, success: true };
      } catch (error) {
        console.error(`普通字段 ${category} 请求失败:`, error);
        return { category, data: {}, success: false, error };
      }
    });

    // 等待所有请求完成
    const [segmentedResults, normalResults] = await Promise.all([Promise.all(segmentedPromises), Promise.all(normalPromises)]);

    // 合并所有数据
    let mergedData = {};

    // 合并分段字段数据
    segmentedResults.forEach(result => {
      if (result.success && result.data) {
        mergedData = merge(mergedData, result.data);
      }
    });

    // 合并普通字段数据
    normalResults.forEach(result => {
      if (result.success && result.data) {
        mergedData = merge(mergedData, result.data);
      }
    });

    // 更新目标数据
    Object.assign(targetData, mergedData);

    console.log(`✅ ${dataType}加载完成，合并数据:`, mergedData);
  } catch (error) {
    console.error(`❌ 加载${dataType}失败:`, error);
    throw error;
  }
};

/**
 * 按需加载特定字段
 */
export const loadDeviceConfigFields = async (fields: string[]): Promise<void> => {
  console.log("🚀 按需加载设备配置字段:", fields);
  // 简化实现，直接调用完整加载
  await loadDeviceConfig();
};

/**
 * 按需加载设备状态字段
 */
export const loadDeviceStatusFields = async (fields: string[]): Promise<void> => {
  console.log("🚀 按需加载设备状态字段:", fields);
  // 简化实现，直接调用完整加载
  await loadDeviceStatus();
};

/**
 * 按标签页加载数据
 */
export const loadDataByTab = async (tab: string): Promise<void> => {
  console.log("🚀 按标签页加载数据:", tab);
  switch (tab) {
    case "port":
      await loadPortRelatedData();
      break;
    case "vlan":
      await loadVlanData();
      break;
    case "topology":
      await loadTopologyData();
      break;
    default:
      await loadDeviceConfig();
      break;
  }
};

/**
 * 加载端口相关数据
 */
export const loadPortRelatedData = async (): Promise<void> => {
  console.log("🚀 加载端口相关数据");
  await loadDeviceConfigFields(["swPort", "swPoe", "swStorm"]);
};

/**
 * 加载VLAN数据
 */
export const loadVlanData = async (): Promise<void> => {
  console.log("🚀 加载VLAN数据");
  await loadDeviceConfigFields(["swVlan"]);
};

/**
 * 加载拓扑数据
 */
export const loadTopologyData = async (): Promise<void> => {
  console.log("🚀 加载拓扑数据");
  await loadDeviceStatusFields(["topology"]);
};

/**
 * 加载系统管理数据
 */
export const loadSystemManagementData = async (): Promise<void> => {
  console.log("🚀 加载系统管理数据");
  await loadDeviceConfigFields(["system"]);
};
