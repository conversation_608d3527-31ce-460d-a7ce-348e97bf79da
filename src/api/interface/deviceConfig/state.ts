import { computed, reactive, ref } from "vue";
import { deviceConfig, drawerProps, swIsolate, swPoe, swPort, swQos, swStorm, swVlan } from "@/api/interface/device/model";
import { getDefaultDeviceStatus } from "./defaults";
import { defaultBeginTime, defaultEndTime } from "./constants";
import type { DeviceStatistics } from "./types";

/**
 * 设备状态数据
 */
export const deviceStatus = reactive(getDefaultDeviceStatus());

/**
 * 抽屉可见性状态
 */
export const drawerVisible = ref(false);

/**
 * 已加载的标签页状态
 */
export const loadedTabs = ref(new Set<string>());

/**
 * 清除已加载的标签页状态
 */
export const clearLoadedTabs = () => {
  loadedTabs.value.clear();
};

/**
 * 当前活动标签页
 */
export const activeName = ref<"first" | "second" | "third" | "fourth" | "fifth">("first");

/**
 * 控制加密状态的变量
 */
export const encryptRadio0 = ref<boolean>(false);
export const encryptRadio1 = ref<boolean>(false);
export const encryptGuest = ref<boolean>(false);

/**
 * 手动设置加密方式的标志
 */
export const manualEncryptRadio0 = ref<boolean>(false);
export const manualEncryptRadio1 = ref<boolean>(false);
export const manualEncryptGuest = ref<boolean>(false);

/**
 * 存储原始的key值
 */
export const originalKey0 = ref<string>("");
export const originalKey1 = ref<string>("");
export const originalKeyGuest = ref<string>("");

/**
 * 设备名称编辑状态
 */
export const editName = ref(false);
export const deviceNameChanged = ref(false);

/**
 * 端口相关状态
 */
export const portDialogVisible = ref(false);
export const selectedRows = ref<any[]>([]);
export const showPortExample = ref(false);
export const portTableRef = ref(null);
export const isolateRows = ref<any[]>([]);
export const rateOptions = ref<Array<any>>([]);

/**
 * 升级相关状态
 */
export const progressShow = ref(false);
export const downloadingPer = ref(0);

/**
 * 设备统计数据
 */
export const deviceWeekStatistic = ref<DeviceStatistics>(null);

/**
 * 时间相关状态
 */
export const beginTime = ref<Date>(
  deviceConfig.wireless?.wifiTime?.beginTime
    ? new Date(`1970-01-01T${deviceConfig.wireless.wifiTime.beginTime}:00`)
    : defaultBeginTime
);

export const endTime = ref<Date>(
  deviceConfig.wireless?.wifiTime?.endTime ? new Date(`1970-01-01T${deviceConfig.wireless.wifiTime.endTime}:00`) : defaultEndTime
);

/**
 * 计算属性：IP前缀
 */
export const startIpPrefix = computed(() => {
  const lanIp = deviceConfig.network?.lan?.ipaddr || "";
  const parts = lanIp.split(".");
  if (parts.length >= 3) {
    return `${parts[0]}.${parts[1]}.${parts[2]}.`;
  }
  return "";
});

/**
 * 计算属性：选中行名称
 */
export const selectedRowNames = computed(() => {
  return selectedRows.value.map(row => row.describe || row.name).join(", ");
});

// 导出所有从 device/model 导入的状态
export { deviceConfig, drawerProps, swIsolate, swPoe, swPort, swQos, swStorm, swVlan };
