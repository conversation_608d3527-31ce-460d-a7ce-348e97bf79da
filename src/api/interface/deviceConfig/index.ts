/**
 * 设备配置相关功能统一导出
 *
 * 这个文件作为 deviceConfig 模块的入口点，
 * 统一导出所有相关的类型、状态、工具函数和API方法
 */

// 导出类型定义
export type { LoadDeviceDataParams, FieldRequestResult, DeviceStatistics } from "./types";

export { DeviceDataCmd, DeviceDataType, PoePower, VlanMode, TrafficType } from "./types";

// 导出常量
export {
  DEFAULT_SEGMENTED_FIELDS,
  FIELD_CATEGORY_MAP,
  poePowerOptions,
  vlanModeOptions,
  trafficTypeOptionsArray,
  defaultBeginTime,
  defaultEndTime
} from "./constants";

// 导出状态管理
export {
  deviceStatus,
  drawerVisible,
  loadedTabs,
  clearLoadedTabs,
  activeName,
  encryptRadio0,
  encryptRadio1,
  encryptGuest,
  manualEncryptRadio0,
  manualEncryptRadio1,
  manualEncryptGuest,
  originalKey0,
  originalKey1,
  originalKeyGuest,
  editName,
  deviceNameChanged,
  portDialogVisible,
  selectedRows,
  showPortExample,
  portTableRef,
  isolateRows,
  rateOptions,
  progressShow,
  downloadingPer,
  deviceWeekStatistic,
  beginTime,
  endTime,
  startIpPrefix,
  selectedRowNames,
  deviceConfig,
  drawerProps,
  swIsolate,
  swPoe,
  swPort,
  swQos,
  swStorm,
  swVlan
} from "./state";

// 导出分段相关功能
export {
  addSegmentedFields,
  removeSegmentedFields,
  setSegmentedFields,
  addFieldCategoryMappings,
  getSegmentedFields,
  getFieldCategoryMap,
  shouldFieldBeSegmented,
  inferFieldCategory
} from "./segmentation";

// 导出数据加载功能
export {
  loadDeviceConfig,
  loadDeviceStatus,
  loadDeviceConfigFields,
  loadDeviceStatusFields,
  loadDataByTab,
  loadPortRelatedData,
  loadVlanData,
  loadTopologyData,
  loadSystemManagementData
} from "./dataLoader";

// 导出设备操作功能
export {
  rebootDevice,
  saveDeviceConfig,
  editDeviceName,
  saveDeviceName,
  upgradeDevice,
  resetToFactory
} from "./deviceOperations";

// 导出端口管理功能
export {
  getPortIcon,
  getPortStatusText,
  getPortSpeedText,
  openPortDialog,
  closePortDialog,
  togglePortExample,
  batchConfigurePorts,
  configureSinglePort,
  configurePoEPort,
  configureVlanPort,
  configurePortIsolation
} from "./portManagement";

// 导出工具函数
export {
  formatBytes,
  formatSpeed,
  formatDuration,
  formatMacAddress,
  formatIpAddress,
  formatPortNumber,
  formatVlanId,
  formatPercentage,
  formatTemperature,
  formatSignalStrength,
  getDeviceTrafficStatistics,
  calculateTrafficGrowthRate,
  getTrafficTrend,
  isValidIpAddress,
  isValidMacAddress,
  generateRandomMacAddress
} from "./utils";

// 导出默认配置
export {
  getDefaultDeviceConfig,
  getDefaultDeviceStatus,
  getDefaultPortConfig,
  getDefaultPoEConfig,
  getDefaultVlanConfig,
  getDefaultIsolationConfig,
  getDefaultStormConfig,
  getDefaultQosConfig,
  getDefaultWirelessConfig,
  getDefaultWanConfig,
  getDefaultLanConfig,
  getDefaultDhcpConfig,
  getDefaultLldpConfig,
  getDefaultRstpConfig,
  getDefaultSystemConfig
} from "./defaults";

// 导出UI辅助函数
export {
  closeDialog,
  showPortDialog,
  handleReboot,
  handleUpgrade,
  getDeviceStatistics,
  handleSelectionChange,
  togglePortSelection,
  toggleRowSelection,
  isPortSelected,
  isRowSelected,
  isolateAll,
  getPortNames,
  getPortStates,
  formatAutoneg,
  generatePortData,
  handleStartIpChange,
  preloadRateOptions,
  speedDuplexOptions,
  selectedTrafficTypes,
  updateTrafficType,
  statusLabel,
  statusTagType,
  encryptionRadio0Method,
  encryptionRadio1Method,
  encryptionGuestMethod
} from "./uiHelpers";
