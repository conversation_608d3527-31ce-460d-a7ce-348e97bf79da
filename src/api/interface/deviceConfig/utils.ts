import { computed } from "vue";
import { useGlobalStore } from "@/stores/modules/global";
import { deviceTrafficWeeklyReport } from "@/api/modules/deviceConfigDrawer";
import { drawerProps, deviceWeekStatistic } from "./state";

/**
 * 格式化字节数为可读格式
 */
export const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
};

/**
 * 格式化速率为可读格式
 */
export const formatSpeed = (bps: number): string => {
  if (bps === 0) return "0 bps";

  const k = 1000; // 网络速率通常使用1000而不是1024
  const sizes = ["bps", "Kbps", "Mbps", "Gbps"];

  const i = Math.floor(Math.log(bps) / Math.log(k));
  const value = (bps / Math.pow(k, i)).toFixed(1);

  return `${value} ${sizes[i]}`;
};

/**
 * 格式化时间为可读格式
 */
export const formatDuration = (seconds: number): string => {
  const globalStore = useGlobalStore();
  const isChinese = computed(() => globalStore.language === "zh");

  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);
  const secs = seconds % 60;

  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days}${isChinese.value ? "天" : "d"}`);
  }
  if (hours > 0) {
    parts.push(`${hours}${isChinese.value ? "小时" : "h"}`);
  }
  if (minutes > 0) {
    parts.push(`${minutes}${isChinese.value ? "分钟" : "m"}`);
  }
  if (secs > 0 || parts.length === 0) {
    parts.push(`${secs}${isChinese.value ? "秒" : "s"}`);
  }

  return parts.join(" ");
};

/**
 * 格式化MAC地址
 */
export const formatMacAddress = (mac: string): string => {
  if (!mac) return "";

  // 移除所有非十六进制字符
  const cleanMac = mac.replace(/[^a-fA-F0-9]/g, "");

  // 每两个字符插入冒号
  return cleanMac
    .replace(/(.{2})/g, "$1:")
    .slice(0, -1)
    .toUpperCase();
};

/**
 * 格式化IP地址
 */
export const formatIpAddress = (ip: string): string => {
  if (!ip) return "";

  // 验证IP地址格式
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  const match = ip.match(ipRegex);

  if (!match) return ip;

  // 确保每个部分都在0-255范围内
  const parts = match.slice(1).map(part => {
    const num = parseInt(part, 10);
    return num >= 0 && num <= 255 ? num.toString() : part;
  });

  return parts.join(".");
};

/**
 * 格式化端口号
 */
export const formatPortNumber = (port: number | string): string => {
  const portNum = typeof port === "string" ? parseInt(port, 10) : port;

  if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
    return String(port);
  }

  return portNum.toString();
};

/**
 * 格式化VLAN ID
 */
export const formatVlanId = (vlanId: number | string): string => {
  const vlan = typeof vlanId === "string" ? parseInt(vlanId, 10) : vlanId;

  if (isNaN(vlan) || vlan < 1 || vlan > 4094) {
    return String(vlanId);
  }

  return vlan.toString();
};

/**
 * 格式化百分比
 */
export const formatPercentage = (value: number, decimals = 1): string => {
  if (isNaN(value)) return "0%";

  return `${value.toFixed(decimals)}%`;
};

/**
 * 格式化温度
 */
export const formatTemperature = (celsius: number): string => {
  if (isNaN(celsius)) return "-";

  return `${celsius.toFixed(1)}°C`;
};

/**
 * 格式化信号强度
 */
export const formatSignalStrength = (rssi: number): string => {
  if (isNaN(rssi)) return "-";

  return `${rssi} dBm`;
};

/**
 * 获取设备流量统计数据
 */
export const getDeviceTrafficStatistics = async (): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;

  if (!deviceId) {
    console.warn("设备ID为空，无法获取流量统计");
    return;
  }

  try {
    console.log("正在获取设备流量统计数据，设备ID:", deviceId);

    const response = await deviceTrafficWeeklyReport({ deviceId });

    if (response && response.code === "200" && response.data) {
      deviceWeekStatistic.value = response.data;
      console.log("设备流量统计数据获取成功:", response.data);
    } else {
      console.warn("获取设备流量统计数据失败:", response);
      deviceWeekStatistic.value = null;
    }
  } catch (error) {
    console.error("获取设备流量统计数据异常:", error);
    deviceWeekStatistic.value = null;
  }
};

/**
 * 计算流量增长率
 */
export const calculateTrafficGrowthRate = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;

  return ((current - previous) / previous) * 100;
};

/**
 * 获取流量趋势
 */
export const getTrafficTrend = (growthRate: number): "up" | "down" | "stable" => {
  if (growthRate > 5) return "up";
  if (growthRate < -5) return "down";
  return "stable";
};

/**
 * 验证IP地址格式
 */
export const isValidIpAddress = (ip: string): boolean => {
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  const match = ip.match(ipRegex);

  if (!match) return false;

  return match.slice(1).every(part => {
    const num = parseInt(part, 10);
    return num >= 0 && num <= 255;
  });
};

/**
 * 验证MAC地址格式
 */
export const isValidMacAddress = (mac: string): boolean => {
  const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
  return macRegex.test(mac);
};

/**
 * 生成随机MAC地址
 */
export const generateRandomMacAddress = (): string => {
  const hexChars = "0123456789ABCDEF";
  let mac = "";

  for (let i = 0; i < 6; i++) {
    if (i > 0) mac += ":";
    mac += hexChars[Math.floor(Math.random() * 16)];
    mac += hexChars[Math.floor(Math.random() * 16)];
  }

  return mac;
};
