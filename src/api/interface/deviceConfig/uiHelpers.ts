import { ref } from "vue";
import { ElMessage } from "element-plus";
import i18n from "@/languages";
import { selectedRows, portDialogVisible, isolateRows, rateOptions } from "./state";
import { rebootDevice, upgradeDevice } from "./deviceOperations";
import { getDeviceTrafficStatistics } from "./utils";

const t = i18n.global.t;

/**
 * 关闭对话框
 */
export const closeDialog = (): void => {
  portDialogVisible.value = false;
  selectedRows.value = [];
};

/**
 * 显示端口对话框
 */
export const showPortDialog = (): void => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning({ message: t("common.pleaseSelectPort") });
    return;
  }
  portDialogVisible.value = true;
};

/**
 * 处理重启
 */
export const handleReboot = async (): Promise<void> => {
  await rebootDevice();
};

/**
 * 处理升级
 */
export const handleUpgrade = async (firmwareUrl?: string): Promise<void> => {
  await upgradeDevice(firmwareUrl || "");
};

/**
 * 获取设备统计数据
 */
export const getDeviceStatistics = async (): Promise<void> => {
  await getDeviceTrafficStatistics();
};

/**
 * 处理选择变更
 */
export const handleSelectionChange = (selection: any[]): void => {
  selectedRows.value = selection;
};

/**
 * 切换端口选择
 */
export const togglePortSelection = (port: any): void => {
  const index = selectedRows.value.findIndex(row => row.port === port.port);
  if (index > -1) {
    selectedRows.value.splice(index, 1);
  } else {
    selectedRows.value.push(port);
  }
};

/**
 * 切换行选择
 */
export const toggleRowSelection = (row: any): void => {
  const index = selectedRows.value.findIndex(r => r.id === row.id);
  if (index > -1) {
    selectedRows.value.splice(index, 1);
  } else {
    selectedRows.value.push(row);
  }
};

/**
 * 检查端口是否被选中
 */
export const isPortSelected = (port: any): boolean => {
  return selectedRows.value.some(row => row.port === port.port);
};

/**
 * 检查行是否被选中
 */
export const isRowSelected = (row: any): boolean => {
  return selectedRows.value.some(r => r.id === row.id);
};

/**
 * 隔离所有端口
 */
export const isolateAll = (): void => {
  isolateRows.value = [...selectedRows.value];
  ElMessage.success({ message: t("common.operationSuccess") });
};

/**
 * 获取端口名称
 */
export const getPortNames = (ports: any[]): string => {
  return ports.map(port => `Port ${port.port}`).join(", ");
};

/**
 * 获取端口状态
 */
export const getPortStates = (ports?: any[]): string => {
  if (!ports || ports.length === 0) return "";
  const onlineCount = ports.filter(port => port.link === 1).length;
  const offlineCount = ports.length - onlineCount;
  return `${t("common.online")}: ${onlineCount}, ${t("common.offline")}: ${offlineCount}`;
};

/**
 * 格式化自协商
 */
export const formatAutoneg = (autoneg: number): string => {
  return autoneg === 1 ? t("common.enabled") : t("common.disabled");
};

/**
 * 生成端口数据
 */
export const generatePortData = (portData: any): any[] => {
  if (Array.isArray(portData)) {
    return portData;
  }

  // 如果是数字，生成对应数量的端口
  if (typeof portData === "number") {
    const ports = [];
    for (let i = 1; i <= portData; i++) {
      ports.push({
        port: i,
        enable: 1,
        link: Math.random() > 0.5 ? 1 : 0,
        speed: 1000,
        duplex: 1,
        poe: i <= 8 ? 1 : 0, // 前8个端口支持PoE
        description: `Port ${i}`
      });
    }
    return ports;
  }

  // 如果是对象，直接返回包装在数组中
  return [portData];
};

/**
 * 处理起始IP变更
 */
export const handleStartIpChange = (value: string): void => {
  console.log("起始IP变更:", value);
  // 这里可以添加IP验证逻辑
};

/**
 * 预加载速率选项
 */
export const preloadRateOptions = (): any[] => {
  const options = [
    { label: "10M", value: 10 },
    { label: "100M", value: 100 },
    { label: "1000M", value: 1000 },
    { label: "Auto", value: 0 }
  ];
  rateOptions.value = options;
  return options;
};

/**
 * 速率双工选项
 */
export const speedDuplexOptions = ref([
  { label: "Auto", value: 0 },
  { label: "10M Half", value: 1 },
  { label: "10M Full", value: 2 },
  { label: "100M Half", value: 3 },
  { label: "100M Full", value: 4 },
  { label: "1000M Full", value: 5 }
]);

/**
 * 选中的流量类型
 */
export const selectedTrafficTypes = ref<number[]>([]);

/**
 * 更新流量类型
 */
export const updateTrafficType = (types: number[]): void => {
  selectedTrafficTypes.value = types;
};

/**
 * 状态标签
 */
export const statusLabel = (status: number): string => {
  return status === 1 ? t("common.online") : t("common.offline");
};

/**
 * 状态标签类型
 */
export const statusTagType = (status: number): string => {
  return status === 1 ? "success" : "danger";
};

/**
 * 加密方法 - Radio0
 */
export const encryptionRadio0Method = ref("none");

/**
 * 加密方法 - Radio1
 */
export const encryptionRadio1Method = ref("none");

/**
 * 加密方法 - Guest
 */
export const encryptionGuestMethod = ref("none");

/**
 * 格式化速率双工
 */
export const formatSpeedDuplex = (value: any): string => {
  if (typeof value === "string") return value;
  if (typeof value === "number") {
    switch (value) {
      case 0:
        return "Auto";
      case 1:
        return "10M Half";
      case 2:
        return "10M Full";
      case 3:
        return "100M Half";
      case 4:
        return "100M Full";
      case 5:
        return "1000M Full";
      default:
        return "Unknown";
    }
  }
  return String(value);
};
