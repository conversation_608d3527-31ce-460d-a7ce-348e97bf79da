import { useUserStore } from "@/stores/modules/user";
import { pushDeviceConfigJwe } from "@/api/modules/project";
import { ElMessage } from "element-plus";
import i18n from "@/languages";
import { drawerProps, selectedRows, portDialogVisible, showPortExample } from "./state";
import portActiveIcon from "@/assets/images/port_active_icon.png";
import portDeactiveIcon from "@/assets/images/port_deactive_icon.png";
import portActiveEleIcon from "@/assets/images/port_active_ele_icon.png";
import portDeactiveEleIcon from "@/assets/images/port_deactive_ele_icon.png";

const t = i18n.global.t;

/**
 * 获取端口图标
 */
export const getPortIcon = (port: any): string => {
  if (port.poe === 1) {
    return port.link === 1 ? portActiveEleIcon : portDeactiveEleIcon;
  } else {
    return port.link === 1 ? portActiveIcon : portDeactiveIcon;
  }
};

/**
 * 获取端口状态文本
 */
export const getPortStatusText = (port: any): string => {
  return port.link === 1 ? t("common.online") : t("common.offline");
};

/**
 * 获取端口速率文本
 */
export const getPortSpeedText = (port: any): string => {
  if (port.link !== 1) return "-";

  const speed = port.speed || 0;
  const duplex = port.duplex || 0;

  if (speed === 0) return "Auto";

  const speedText = speed >= 1000 ? `${speed / 1000}G` : `${speed}M`;
  const duplexText = duplex === 1 ? "Full" : "Half";

  return `${speedText} ${duplexText}`;
};

/**
 * 打开端口配置对话框
 */
export const openPortDialog = (rows: any[]): void => {
  selectedRows.value = rows;
  portDialogVisible.value = true;
};

/**
 * 关闭端口配置对话框
 */
export const closePortDialog = (): void => {
  portDialogVisible.value = false;
  selectedRows.value = [];
};

/**
 * 显示端口示例
 */
export const togglePortExample = (): void => {
  showPortExample.value = !showPortExample.value;
};

/**
 * 批量配置端口
 */
export const batchConfigurePorts = async (config: any): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  if (selectedRows.value.length === 0) {
    ElMessage.error({ message: t("common.noPortSelected") });
    return;
  }

  try {
    const portConfigs = selectedRows.value.map(port => ({
      port: port.port,
      ...config
    }));

    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          swPort: portConfigs
        }
      }
    };

    console.log("批量配置端口参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.operationSuccess") });
    closePortDialog();
  } catch (error) {
    console.error("批量配置端口失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 配置单个端口
 */
export const configureSinglePort = async (portIndex: number, config: any): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          swPort: [
            {
              port: portIndex,
              ...config
            }
          ]
        }
      }
    };

    console.log("配置单个端口参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.operationSuccess") });
  } catch (error) {
    console.error("配置单个端口失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 配置PoE端口
 */
export const configurePoEPort = async (portIndex: number, poeConfig: any): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        system: {
          swPoe: [
            {
              port: portIndex,
              ...poeConfig
            }
          ]
        }
      }
    };

    console.log("配置PoE端口参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.operationSuccess") });
  } catch (error) {
    console.error("配置PoE端口失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 配置VLAN端口
 */
export const configureVlanPort = async (portIndex: number, vlanConfig: any): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        network: {
          swVlan: [
            {
              port: portIndex,
              ...vlanConfig
            }
          ]
        }
      }
    };

    console.log("配置VLAN端口参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.operationSuccess") });
  } catch (error) {
    console.error("配置VLAN端口失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};

/**
 * 配置端口隔离
 */
export const configurePortIsolation = async (isolationConfig: any[]): Promise<void> => {
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  if (!deviceId) {
    ElMessage.error({ message: t("common.deviceIdRequired") });
    return;
  }

  try {
    const params = {
      cmd: 6,
      deviceId,
      userId,
      data: {
        network: {
          swIsolate: isolationConfig
        }
      }
    };

    console.log("配置端口隔离参数:", params);
    const response = await pushDeviceConfigJwe(params);

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.operationFailed") });
      return;
    }

    ElMessage.success({ message: t("common.operationSuccess") });
  } catch (error) {
    console.error("配置端口隔离失败:", error);
    ElMessage.error({ message: t("common.operationFailed") });
  }
};
