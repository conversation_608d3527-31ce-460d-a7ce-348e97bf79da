import type { Project } from "@/api/interface/project";

/**
 * 网桥客户端数据接口
 */
export interface BridgeClientData extends Partial<Project.ResDeviceList> {
  extra?: {
    type?: number;
    model?: string;
    version?: string;
    macaddr?: string;
    txByte?: number;
    rxByte?: number;
    txRate?: number;
    rxRate?: number;
    latency?: number;
    time?: number;
    rssi?: number;
    online?: number;
  };
  name?: string;
  brClient?: any[];
}

/**
 * 抽屉组件属性接口
 */
export interface DrawerProps {
  title: string;
  isView: boolean;
  row: BridgeClientData;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

/**
 * 拓扑图节点数据接口
 */
export interface TopologyNode {
  name: string;
  symbol: string;
  children?: TopologyNode[];
  extra?: any;
}
