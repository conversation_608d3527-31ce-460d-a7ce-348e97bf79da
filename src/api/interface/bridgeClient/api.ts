import { nextTick } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { pushDeviceConfigJwe } from "@/api/modules/project";
import { ElMessage } from "element-plus";
import { drawerProps, data } from "./state";
import { getBridgeMainIcon, getDeviceIcon, loadImageAsBase64 } from "./icons";

/**
 * 生成拓扑图数据
 * @param row 设备数据
 * @param t 国际化函数
 */
export const generateData = async (row: any, t: any) => {
  // 动态加载 bridgeIcon_main 为 Base64
  const bridgeIconMainBase64 = await loadImageAsBase64(getBridgeMainIcon());

  // 更新数据
  data.children = [
    {
      name: row.deviceName ? row.deviceName : row.deviceId,
      symbol: `image://${bridgeIconMainBase64}`, // 使用 Base64
      children: [],
      extra: {}
    }
  ];

  if (row.brClient) {
    let children = [];
    row.brClient.sort((a: any, b: any) => b.online - a.online);
    row.brClient.forEach((item: any) => {
      children.push({
        name: item.name ? item.name : t("terminal.terminalType.unknown"),
        symbol: "image://" + getDeviceIcon(item.type, item.online),
        extra: item
      });
    });
    data.children[0].children = children;
  }

  // 确保视图更新
  await nextTick();
  console.log("DOM 已更新，当前 data.children:", data.children);
};

/**
 * 保存设备名称
 * @param row 设备数据
 * @param t 国际化函数
 */
export const saveDeviceName = async (row: any, t: any) => {
  console.log("saveDeviceName called. row:", JSON.stringify(row));
  const params = {
    cmd: 6,
    deviceId: drawerProps.value.row.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      network: {
        brClient: [
          {
            name: row.name,
            macaddr: row.extra.macaddr
          }
        ]
      }
    }
  };
  console.log("发送的参数:", params);
  const response = await pushDeviceConfigJwe(params);
  console.log("API 响应数据:", response);
  if (!response || response.code !== "200") {
    ElMessage.error({ message: response.msg });
    return;
  }
  ElMessage.success({ message: t("common.operationSuccess") });
};

/**
 * 删除网桥客户端
 * @param row 设备数据
 * @param t 国际化函数
 */
export const deleteBridgeClient = async (row: any, t: any) => {
  console.log("deleteBridgeClient called. row:", JSON.stringify(row));
  const params = {
    cmd: 6,
    deviceId: drawerProps.value.row.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      network: {
        brClient: [
          {
            macaddr: row.extra.macaddr,
            delete: 1
          }
        ]
      }
    }
  };
  console.log("发送的参数:", params);
  const response = await pushDeviceConfigJwe(params);
  console.log("API 响应数据:", response);
  if (!response || response.code !== "200") {
    ElMessage.error({ message: response.msg });
    return;
  }
  ElMessage.success({ message: t("common.operationSuccess") });
};
