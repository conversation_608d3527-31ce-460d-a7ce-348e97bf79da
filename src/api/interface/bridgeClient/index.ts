/**
 * 网桥客户端相关功能统一导出
 *
 * 这个文件作为 bridgeClient 模块的入口点，
 * 统一导出所有相关的类型、状态、工具函数和API方法
 */

// 导出类型定义
export type { BridgeClientData, DrawerProps, TopologyNode } from "./types";

// 导出状态管理
export {
  drawerVisible,
  drawerProps,
  clickNodeProps,
  data,
  editName,
  deviceNameChanged,
  bridgeClientDrawerVisible,
  editDeviceName
} from "./state";

// 导出工具函数
export { formatBootTime } from "./utils";

// 导出图标相关功能
export { getInternetIcon, getBridgeMainIcon, getDeviceIcon, loadImageAsBase64 } from "./icons";

// 导出API相关功能
export { generateData, saveDeviceName, deleteBridgeClient } from "./api";
