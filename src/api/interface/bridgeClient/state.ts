import { reactive, ref } from "vue";
import type { DrawerProps } from "./types";
import { getInternetIcon } from "./icons";

/**
 * 抽屉可见性状态
 */
export const drawerVisible = ref(false);

/**
 * 抽屉属性状态
 */
export const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

/**
 * 点击节点属性状态
 */
export const clickNodeProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

/**
 * 拓扑图数据状态
 */
export const data = reactive({
  name: "internet",
  symbol: "image://" + getInternetIcon(),
  children: []
});

/**
 * 编辑名称状态
 */
export const editName = ref(false); // 是否编辑网桥客户端名称

/**
 * 设备名称变更状态
 */
export const deviceNameChanged = ref(false); // 是否修改网桥客户端名称

/**
 * 网桥客户端抽屉可见性状态
 */
export const bridgeClientDrawerVisible = ref(false);

/**
 * 编辑设备名称
 */
export const editDeviceName = (row: any) => {
  console.log("editDeviceName called. row:", row);
  editName.value = !editName.value;
};
