import bridgeIcon_main from "@/assets/images/bridge_center_icon.png";
import bridgeIcon_onLine from "@/assets/images/device3_icon.png";
import bridgeIcon_offline from "@/assets/images/device4_icon.png";
import otherDeviceIcon_online from "@/assets/images/device_other_online_icon.png";
import otherDeviceIcon_offline from "@/assets/images/device_other_offline_icon.png";
import internetIcon from "@/assets/images/internet_icon.png";

/**
 * 获取互联网图标
 */
export const getInternetIcon = (): string => {
  return internetIcon;
};

/**
 * 获取主网桥图标
 */
export const getBridgeMainIcon = (): string => {
  return bridgeIcon_main;
};

/**
 * 根据设备类型和在线状态获取设备图标
 */
export const getDeviceIcon = (type: number, online: number): string => {
  switch (type) {
    case 1:
      return online === 1 ? bridgeIcon_onLine : bridgeIcon_offline;
    default:
      return online === 1 ? otherDeviceIcon_online : otherDeviceIcon_offline;
  }
};

/**
 * 将图片URL转换为Base64格式
 */
export const loadImageAsBase64 = async (url: string): Promise<string> => {
  const response = await fetch(url);
  const blob = await response.blob();
  return new Promise(resolve => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.readAsDataURL(blob);
  });
};
